"use client"

import { useState, useEffect } from "react"
import { useR<PERSON>er, useSearchParams } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Mail, CheckCircle, AlertCircle, Loader2 } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { toast } from "sonner"

export default function VerifyEmailPage() {
    const router = useRouter()
    const searchParams = useSearchParams()
    const { verifyEmail } = useAuth()
    const [isLoading, setIsLoading] = useState(false)
    const [isVerified, setIsVerified] = useState(false)
    const [verificationError, setVerificationError] = useState<string | null>(null)
    const [email, setEmail] = useState("")
    const [verificationToken, setVerificationToken] = useState("")

    useEffect(() => {
        // Get token from URL params
        const token = searchParams.get("token")
        const emailParam = searchParams.get("email")

        if (token) {
            setVerificationToken(token)
        }
        if (emailParam) {
            setEmail(emailParam)
        }
    }, [searchParams])

    const handleVerifyEmail = async (e: React.FormEvent) => {
        e.preventDefault()
        if (!verificationToken) {
            toast.error("Verification token is required")
            return
        }

        setIsLoading(true)
        setVerificationError(null)

        try {
            const { error } = await verifyEmail(verificationToken)

            if (!error) {
                setIsVerified(true)
                toast.success("Email verified successfully!")
                setTimeout(() => {
                    router.push("/")
                }, 2000)
            } else {
                setVerificationError("Invalid or expired verification token")
                toast.error("Verification failed")
            }
        } catch (error) {
            setVerificationError("An error occurred during verification")
            toast.error("Verification failed")
        } finally {
            setIsLoading(false)
        }
    }

    const handleResendVerification = () => {
        toast.info("Verification email resent! Check your inbox.")
    }

    if (isVerified) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
                <Card className="w-full max-w-md">
                    <CardHeader className="text-center">
                        <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                            <CheckCircle className="w-8 h-8 text-green-600" />
                        </div>
                        <CardTitle className="text-2xl font-bold text-green-600">Email Verified!</CardTitle>
                    </CardHeader>
                    <CardContent className="text-center space-y-4">
                        <p className="text-gray-600">
                            Your email has been successfully verified. You can now access your account.
                        </p>
                        <div className="flex justify-center">
                            <Loader2 className="w-6 h-6 text-primary animate-spin" />
                        </div>
                        <p className="text-sm text-gray-500">Redirecting to dashboard...</p>
                    </CardContent>
                </Card>
            </div>
        )
    }

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
            <Card className="w-full max-w-md">
                <CardHeader className="text-center">
                    <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                        <Mail className="w-8 h-8 text-blue-600" />
                    </div>
                    <CardTitle className="text-2xl font-bold">Verify Your Email</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                    <div className="text-center space-y-2">
                        <p className="text-gray-600">
                            We've sent a verification link to your email address. Please check your inbox and click the verification link.
                        </p>
                        {email && (
                            <p className="text-sm text-gray-500">
                                Email: <span className="font-medium">{email}</span>
                            </p>
                        )}
                    </div>

                    {verificationError && (
                        <div className="flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                            <AlertCircle className="w-5 h-5 text-red-600" />
                            <p className="text-sm text-red-600">{verificationError}</p>
                        </div>
                    )}

                    <form onSubmit={handleVerifyEmail} className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="verification-token">Verification Token</Label>
                            <Input
                                id="verification-token"
                                placeholder="Enter verification token from email"
                                value={verificationToken}
                                onChange={(e) => setVerificationToken(e.target.value)}
                                required
                                disabled={isLoading}
                            />
                        </div>

                        <Button
                            type="submit"
                            className="w-full"
                            disabled={isLoading || !verificationToken}
                        >
                            {isLoading ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Verifying...
                                </>
                            ) : (
                                "Verify Email"
                            )}
                        </Button>
                    </form>

                    <div className="text-center space-y-4">
                        <div className="border-t pt-4">
                            <p className="text-sm text-gray-500 mb-2">Didn't receive the email?</p>
                            <Button
                                variant="outline"
                                onClick={handleResendVerification}
                                disabled={isLoading}
                                className="w-full"
                            >
                                Resend Verification Email
                            </Button>
                        </div>

                        <div className="border-t pt-4">
                            <p className="text-sm text-gray-500 mb-2">Already verified?</p>
                            <Button
                                variant="link"
                                onClick={() => router.push("/auth")}
                                className="w-full"
                            >
                                Go to Login
                            </Button>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
} 