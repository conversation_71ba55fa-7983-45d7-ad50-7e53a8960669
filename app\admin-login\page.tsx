"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ShieldCheck, Mail, Lock, Eye, EyeOff } from "lucide-react"
import { SupabaseAuthService } from "@/services/supabase-auth.service"

export default function AdminLoginPage() {
    const router = useRouter()
    const [email, setEmail] = useState("")
    const [password, setPassword] = useState("")
    const [showPassword, setShowPassword] = useState(false)
    const [isLoading, setIsLoading] = useState(false)

    // Check if already authenticated
    useEffect(() => {
        const checkAuth = async () => {
            const { session } = await SupabaseAuthService.getSession()
            if (session) {
                const { data: userProfile } = await SupabaseAuthService.getUserProfile(session.user.id)
                if (userProfile?.role === 'admin') {
                    router.replace('/admin/dashboard')
                }
            }
        }
        checkAuth()
    }, [router])

    const handleAdminLogin = async (e: React.FormEvent) => {
        e.preventDefault()

        if (!email || !password) {
            toast.error("Please fill in all fields")
            return
        }

        // Only allow admin login from this specific route
        if (window.location.pathname !== '/admin-login') {
            toast.error("Unauthorized access")
            return
        }

        setIsLoading(true)

        try {
            const { data, error } = await SupabaseAuthService.signIn(email, password)

            if (error) {
                toast.error(error.message || "Login failed")
                return
            }

            if (data.user) {
                // Check if user is admin
                const { data: userProfile, error: profileError } = await SupabaseAuthService.getUserProfile(data.user.id)

                if (profileError || !userProfile) {
                    toast.error("Failed to load user profile")
                    await SupabaseAuthService.signOut()
                    return
                }

                if (userProfile.role !== 'admin') {
                    toast.error("Access denied. Admin privileges required.")
                    await SupabaseAuthService.signOut()
                    return
                }

                // Set admin session cookie for middleware
                document.cookie = "adminAuthenticated=true; path=/; max-age=86400; SameSite=Lax; Secure"

                toast.success("Admin access granted")
                router.push("/admin/dashboard")
            }
        } catch (error) {
            console.error("Admin login error:", error)
            toast.error("Authentication failed")
        } finally {
            setIsLoading(false)
        }
    }

    return (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
            <div className="w-full max-w-md p-8 space-y-8 bg-white rounded-xl shadow-2xl border border-gray-200">
                <div className="text-center">
                    <div className="mx-auto h-16 w-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full flex items-center justify-center">
                        <ShieldCheck className="h-8 w-8 text-white" />
                    </div>
                    <h2 className="mt-6 text-3xl font-bold text-gray-900">Admin Portal</h2>
                    <p className="mt-2 text-sm text-gray-600">Secure access for administrators only</p>
                    <div className="mt-2 text-xs text-red-600 bg-red-50 px-3 py-1 rounded-full inline-block">
                        🔒 Restricted Access - website.com/admin-login only
                    </div>
                </div>

                <form className="mt-8 space-y-6" onSubmit={handleAdminLogin}>
                    <div className="space-y-4">
                        <div className="relative">
                            <Label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                                Admin Email
                            </Label>
                            <div className="relative">
                                <Mail className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                                <Input
                                    id="email"
                                    name="email"
                                    type="email"
                                    required
                                    className="pl-10 py-3 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                    placeholder="<EMAIL>"
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                />
                            </div>
                        </div>

                        <div className="relative">
                            <Label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                                Password
                            </Label>
                            <div className="relative">
                                <Lock className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                                <Input
                                    id="password"
                                    name="password"
                                    type={showPassword ? "text" : "password"}
                                    required
                                    className="pl-10 pr-10 py-3 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                    placeholder="Enter admin password"
                                    value={password}
                                    onChange={(e) => setPassword(e.target.value)}
                                />
                                <button
                                    type="button"
                                    className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                                    onClick={() => setShowPassword(!showPassword)}
                                >
                                    {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                                </button>
                            </div>
                        </div>
                    </div>

                    <div>
                        <Button
                            type="submit"
                            className="w-full py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-[1.02]"
                            disabled={isLoading}
                        >
                            {isLoading ? (
                                <div className="flex items-center justify-center">
                                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                                    Authenticating...
                                </div>
                            ) : (
                                "Access Admin Dashboard"
                            )}
                        </Button>
                    </div>

                    <div className="text-center">
                        <p className="text-xs text-gray-500">
                            Only authorized administrators can access this portal
                        </p>
                    </div>
                </form>
            </div>
        </div>
    )
}
