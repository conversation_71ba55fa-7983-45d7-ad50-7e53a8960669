import { supabase } from '@/lib/supabase/client'
import { Database } from '@/types/supabase'

type Tables = Database['public']['Tables']
type Car = Tables['cars']['Row']
type CarInsert = Tables['cars']['Insert']
type CarUpdate = Tables['cars']['Update']
type Agency = Tables['agencies']['Row']
type AgencyInsert = Tables['agencies']['Insert']
type AgencyUpdate = Tables['agencies']['Update']
type Booking = Tables['bookings']['Row']
type BookingInsert = Tables['bookings']['Insert']
type BookingUpdate = Tables['bookings']['Update']
type Review = Tables['reviews']['Row']
type ReviewInsert = Tables['reviews']['Insert']
type Payment = Tables['payments']['Row']
type PaymentInsert = Tables['payments']['Insert']
type Notification = Tables['notifications']['Row']
type NotificationInsert = Tables['notifications']['Insert']
type Blog = Tables['blogs']['Row']
type BlogInsert = Tables['blogs']['Insert']
type BlogUpdate = Tables['blogs']['Update']
type Message = Tables['messages']['Row']
type MessageInsert = Tables['messages']['Insert']
type MessageUpdate = Tables['messages']['Update']
type AgencyDocument = Tables['agency_documents']['Row']
type AgencyDocumentInsert = Tables['agency_documents']['Insert']
type AgencyDocumentUpdate = Tables['agency_documents']['Update']

export class DatabaseService {
  // Car operations
  static async getCars(filters?: {
    agency_id?: string
    status?: Car['status']
    location?: string
    city?: string
    fuel_type?: Car['fuel_type']
    transmission?: Car['transmission']
    brand?: string
    color?: string
    category?: string
    min_price?: number
    max_price?: number
    min_year?: number
    max_year?: number
    seats?: number
  }) {
    let query = supabase
      .from('cars')
      .select(`
        *,
        agencies (
          agency_name,
          agency_logo,
          rating,
          is_approved
        )
      `)

    if (filters?.agency_id) {
      query = query.eq('agency_id', filters.agency_id)
    }
    if (filters?.status) {
      query = query.eq('status', filters.status)
    }
    if (filters?.city) {
      query = query.ilike('city', `%${filters.city}%`)
    }
    if (filters?.brand) {
      query = query.eq('brand', filters.brand)
    }
    if (filters?.color) {
      query = query.eq('color', filters.color)
    }
    if (filters?.category) {
      query = query.eq('category', filters.category)
    }
    if (filters?.fuel_type) {
      query = query.eq('fuel_type', filters.fuel_type)
    }
    if (filters?.transmission) {
      query = query.eq('transmission', filters.transmission)
    }
    if (filters?.min_price) {
      query = query.gte('daily_rate', filters.min_price)
    }
    if (filters?.max_price) {
      query = query.lte('daily_rate', filters.max_price)
    }
    if (filters?.min_year) {
      query = query.gte('year', filters.min_year)
    }
    if (filters?.max_year) {
      query = query.lte('year', filters.max_year)
    }
    if (filters?.seats) {
      query = query.eq('seats', filters.seats)
    }

    const { data, error } = await query
      .order('is_featured', { ascending: false })
      .order('view_count', { ascending: false })
      .order('created_at', { ascending: false })
    return { data, error }
  }

  static async incrementCarViewCount(carId: string) {
    const { error } = await supabase.rpc('increment_car_view_count', { car_id: carId })
    return { error }
  }

  static async getCarById(id: string) {
    const { data, error } = await supabase
      .from('cars')
      .select(`
        *,
        agencies (
          agency_name,
          agency_logo,
          agency_phone,
          agency_email,
          rating
        )
      `)
      .eq('id', id)
      .single()

    return { data, error }
  }

  static async createCar(car: CarInsert) {
    const { data, error } = await supabase
      .from('cars')
      .insert(car)
      .select()
      .single()

    return { data, error }
  }

  static async updateCar(id: string, updates: CarUpdate) {
    const { data, error } = await supabase
      .from('cars')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    return { data, error }
  }

  static async deleteCar(id: string) {
    const { error } = await supabase
      .from('cars')
      .delete()
      .eq('id', id)

    return { error }
  }

  // Agency operations
  static async getAgencies(filters?: {
    is_approved?: boolean
    rating_min?: number
  }) {
    let query = supabase
      .from('agencies')
      .select(`
        *,
        users (
          first_name,
          last_name,
          email,
          phone
        )
      `)

    if (filters?.is_approved !== undefined) {
      query = query.eq('is_approved', filters.is_approved)
    }
    if (filters?.rating_min) {
      query = query.gte('rating', filters.rating_min)
    }

    const { data, error } = await query.order('created_at', { ascending: false })
    return { data, error }
  }

  static async getAgencyById(id: string) {
    const { data, error } = await supabase
      .from('agencies')
      .select(`
        *,
        users (
          first_name,
          last_name,
          email,
          phone
        ),
        cars (
          id,
          brand,
          model,
          year,
          daily_rate,
          status,
          images
        )
      `)
      .eq('id', id)
      .single()

    return { data, error }
  }

  static async updateAgency(id: string, updates: AgencyUpdate) {
    const { data, error } = await supabase
      .from('agencies')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    return { data, error }
  }

  // Booking operations
  static async getBookings(filters?: {
    user_id?: string
    agency_id?: string
    status?: Booking['status']
    start_date?: string
    end_date?: string
  }) {
    let query = supabase
      .from('bookings')
      .select(`
        *,
        cars (
          brand,
          model,
          year,
          license_plate,
          images
        ),
        agencies (
          agency_name,
          agency_phone,
          agency_email
        ),
        users (
          first_name,
          last_name,
          email,
          phone
        )
      `)

    if (filters?.user_id) {
      query = query.eq('user_id', filters.user_id)
    }
    if (filters?.agency_id) {
      query = query.eq('agency_id', filters.agency_id)
    }
    if (filters?.status) {
      query = query.eq('status', filters.status)
    }
    if (filters?.start_date) {
      query = query.gte('start_date', filters.start_date)
    }
    if (filters?.end_date) {
      query = query.lte('end_date', filters.end_date)
    }

    const { data, error } = await query.order('created_at', { ascending: false })
    return { data, error }
  }

  static async getBookingById(id: string) {
    const { data, error } = await supabase
      .from('bookings')
      .select(`
        *,
        cars (
          brand,
          model,
          year,
          license_plate,
          images,
          daily_rate
        ),
        agencies (
          agency_name,
          agency_phone,
          agency_email,
          agency_address
        ),
        users (
          first_name,
          last_name,
          email,
          phone
        ),
        payments (
          id,
          amount,
          status,
          payment_date
        )
      `)
      .eq('id', id)
      .single()

    return { data, error }
  }

  static async createBooking(booking: BookingInsert) {
    const { data, error } = await supabase
      .from('bookings')
      .insert(booking)
      .select()
      .single()

    return { data, error }
  }

  static async createGuestBooking(guestBooking: {
    car_id: string
    agency_id: string
    start_date: string
    end_date: string
    pickup_location: string
    return_location: string
    total_price: number
    guest_name: string
    guest_email: string
    guest_phone: string
    guest_address?: string
    special_requests?: string
  }) {
    const bookingData: any = {
      ...guestBooking,
      is_guest_booking: true,
      user_id: null
    }

    const { data, error } = await supabase
      .from('bookings')
      .insert(bookingData)
      .select()
      .single()

    return { data, error }
  }

  static async updateBooking(id: string, updates: BookingUpdate) {
    const { data, error } = await supabase
      .from('bookings')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    return { data, error }
  }

  // Review operations
  static async getReviews(filters?: {
    agency_id?: string
    car_id?: string
    user_id?: string
    status?: Review['status']
  }) {
    let query = supabase
      .from('reviews')
      .select(`
        *,
        users (
          first_name,
          last_name,
          avatar
        ),
        cars (
          brand,
          model,
          year
        ),
        agencies (
          agency_name
        )
      `)

    if (filters?.agency_id) {
      query = query.eq('agency_id', filters.agency_id)
    }
    if (filters?.car_id) {
      query = query.eq('car_id', filters.car_id)
    }
    if (filters?.user_id) {
      query = query.eq('user_id', filters.user_id)
    }
    if (filters?.status) {
      query = query.eq('status', filters.status)
    }

    const { data, error } = await query.order('created_at', { ascending: false })
    return { data, error }
  }

  static async createReview(review: ReviewInsert) {
    const { data, error } = await supabase
      .from('reviews')
      .insert(review)
      .select()
      .single()

    return { data, error }
  }

  // Payment operations
  static async createPayment(payment: PaymentInsert) {
    const { data, error } = await supabase
      .from('payments')
      .insert(payment)
      .select()
      .single()

    return { data, error }
  }

  static async updatePaymentStatus(id: string, status: Payment['status']) {
    const { data, error } = await supabase
      .from('payments')
      .update({ status })
      .eq('id', id)
      .select()
      .single()

    return { data, error }
  }

  // Notification operations
  static async createNotification(notification: NotificationInsert) {
    const { data, error } = await supabase
      .from('notifications')
      .insert(notification)
      .select()
      .single()

    return { data, error }
  }

  static async getUserNotifications(userId: string, unreadOnly = false) {
    let query = supabase
      .from('notifications')
      .select('*')
      .eq('user_id', userId)

    if (unreadOnly) {
      query = query.eq('is_read', false)
    }

    const { data, error } = await query.order('created_at', { ascending: false })
    return { data, error }
  }

  static async markNotificationAsRead(id: string) {
    const { data, error } = await supabase
      .from('notifications')
      .update({ is_read: true })
      .eq('id', id)
      .select()
      .single()

    return { data, error }
  }

  // Blog operations
  static async getBlogs(filters?: {
    status?: Blog['status']
    author_id?: string
    published?: boolean
  }) {
    let query = supabase
      .from('blogs')
      .select(`
        *,
        users (
          first_name,
          last_name,
          avatar
        )
      `)

    if (filters?.status) {
      query = query.eq('status', filters.status)
    }
    if (filters?.author_id) {
      query = query.eq('author_id', filters.author_id)
    }
    if (filters?.published) {
      query = query.eq('status', 'published').not('published_at', 'is', null)
    }

    const { data, error } = await query.order('created_at', { ascending: false })
    return { data, error }
  }

  static async getBlogBySlug(slug: string) {
    const { data, error } = await supabase
      .from('blogs')
      .select(`
        *,
        users (
          first_name,
          last_name,
          avatar
        )
      `)
      .eq('slug', slug)
      .eq('status', 'published')
      .single()

    return { data, error }
  }

  static async createBlog(blog: BlogInsert) {
    const { data, error } = await supabase
      .from('blogs')
      .insert(blog)
      .select()
      .single()

    return { data, error }
  }

  static async updateBlog(id: string, updates: BlogUpdate) {
    const { data, error } = await supabase
      .from('blogs')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    return { data, error }
  }

  static async deleteBlog(id: string) {
    const { error } = await supabase
      .from('blogs')
      .delete()
      .eq('id', id)

    return { error }
  }

  // Message operations
  static async getMessages(filters?: {
    status?: Message['status']
    type?: Message['type']
    priority?: Message['priority']
  }) {
    let query = supabase
      .from('messages')
      .select(`
        *,
        users (
          first_name,
          last_name,
          email
        ),
        bookings (
          id,
          start_date,
          end_date,
          cars (
            brand,
            model
          )
        )
      `)

    if (filters?.status) {
      query = query.eq('status', filters.status)
    }
    if (filters?.type) {
      query = query.eq('type', filters.type)
    }
    if (filters?.priority) {
      query = query.eq('priority', filters.priority)
    }

    const { data, error } = await query.order('created_at', { ascending: false })
    return { data, error }
  }

  static async createMessage(message: MessageInsert) {
    const { data, error } = await supabase
      .from('messages')
      .insert(message)
      .select()
      .single()

    return { data, error }
  }

  static async updateMessage(id: string, updates: MessageUpdate) {
    const { data, error } = await supabase
      .from('messages')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    return { data, error }
  }

  static async markMessageAsRead(id: string) {
    const { data, error } = await supabase
      .from('messages')
      .update({ status: 'read' })
      .eq('id', id)
      .select()
      .single()

    return { data, error }
  }

  // Agency document operations
  static async getAgencyDocuments(agencyId: string, publicOnly = false) {
    let query = supabase
      .from('agency_documents')
      .select('*')
      .eq('agency_id', agencyId)

    if (publicOnly) {
      query = query.eq('is_public', true)
    }

    const { data, error } = await query.order('created_at', { ascending: false })
    return { data, error }
  }

  static async createAgencyDocument(document: AgencyDocumentInsert) {
    const { data, error } = await supabase
      .from('agency_documents')
      .insert(document)
      .select()
      .single()

    return { data, error }
  }

  static async updateAgencyDocument(id: string, updates: AgencyDocumentUpdate) {
    const { data, error } = await supabase
      .from('agency_documents')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    return { data, error }
  }

  static async verifyAgency(agencyId: string, adminId: string) {
    const { data, error } = await supabase
      .from('agencies')
      .update({
        is_approved: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', agencyId)
      .select()
      .single()

    return { data, error }
  }
}
