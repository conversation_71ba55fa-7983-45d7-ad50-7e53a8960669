import { supabase } from '@/lib/supabase/client'
import { Database } from '@/types/supabase'

type Tables = Database['public']['Tables']
type User = Tables['users']['Row']
type UserInsert = Tables['users']['Insert']
type Agency = Tables['agencies']['Row']
type AgencyInsert = Tables['agencies']['Insert']

export class SupabaseAuthService {
    static async signUp(email: string, password: string, userData: Partial<UserInsert>) {
        const { data, error } = await supabase.auth.signUp({
            email,
            password,
            options: {
                data: userData,
                emailRedirectTo: `${window.location.origin}/auth/callback`
            }
        })

        if (!error && data.user) {
            // Insert user profile into public.users table
            const { error: insertError } = await supabase
                .from('users')
                .insert({
                    id: data.user.id,
                    email: data.user.email!,
                    first_name: userData.first_name || '',
                    last_name: userData.last_name || '',
                    phone: userData.phone,
                    role: userData.role || 'user',
                })
            
            if (insertError) {
                console.error('Failed to insert user profile:', insertError)
                return { data, error: insertError }
            }
        }

        return { data, error }
    }

    static async signIn(email: string, password: string) {
        const { data, error } = await supabase.auth.signInWithPassword({
            email,
            password,
        })

        return { data, error }
    }

    static async signOut() {
        const { error } = await supabase.auth.signOut()
        return { error }
    }

    static async getCurrentUser() {
        const { data: { user } } = await supabase.auth.getUser()
        return user
    }

    static async getUserProfile(userId: string): Promise<{ data: User | null, error: any }> {
        const { data, error } = await supabase
            .from('users')
            .select('*')
            .eq('id', userId)
            .single()

        return { data, error }
    }

    static async updateUserProfile(userId: string, updates: Partial<User>) {
        const { data, error } = await supabase
            .from('users')
            .update(updates)
            .eq('id', userId)
            .select()
            .single()

        return { data, error }
    }

    static async resetPassword(email: string) {
        const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
            redirectTo: `${window.location.origin}/auth/reset-password`
        })
        return { data, error }
    }

    static async updatePassword(password: string) {
        const { data, error } = await supabase.auth.updateUser({
            password
        })
        return { data, error }
    }

    static async verifyEmail(token: string) {
        const { data, error } = await supabase.auth.verifyOtp({
            token_hash: token,
            type: 'email'
        })
        return { data, error }
    }

    static async resendVerification(email: string) {
        const { data, error } = await supabase.auth.resend({
            type: 'signup',
            email,
            options: {
                emailRedirectTo: `${window.location.origin}/auth/callback`
            }
        })
        return { data, error }
    }

    // Agency-specific methods
    static async registerAgency(userData: {
        email: string
        password: string
        first_name: string
        last_name: string
        phone?: string
        agency_name: string
        agency_description?: string
        agency_address?: string
        agency_phone?: string
        agency_website?: string
    }) {
        try {
            // First create the user account
            const { data: authData, error: authError } = await this.signUp(
                userData.email,
                userData.password,
                {
                    first_name: userData.first_name,
                    last_name: userData.last_name,
                    phone: userData.phone,
                    role: 'agency'
                }
            )

            if (authError || !authData.user) {
                console.error('Auth error during agency registration:', authError)
                return { data: null, error: authError }
            }

            // Wait a moment for the user to be fully created
            await new Promise(resolve => setTimeout(resolve, 1000))

            // Then create the agency record
            const { data: agencyData, error: agencyError } = await supabase
                .from('agencies')
                .insert({
                    user_id: authData.user.id,
                    agency_name: userData.agency_name,
                    agency_description: userData.agency_description,
                    agency_address: userData.agency_address,
                    agency_phone: userData.agency_phone,
                    agency_email: userData.email,
                    agency_website: userData.agency_website,
                    is_approved: false
                })
                .select()
                .single()

            if (agencyError) {
                console.error('Agency creation error:', agencyError)
                // If agency creation fails, we should clean up the user account
                await supabase.auth.admin.deleteUser(authData.user.id)
                return { data: null, error: agencyError }
            }

            return { data: { user: authData.user, agency: agencyData }, error: null }
        } catch (error) {
            console.error('Unexpected error during agency registration:', error)
            return { data: null, error: { message: 'Registration failed. Please try again.' } }
        }
    }

    static async checkUserRole(userId: string): Promise<'user' | 'agency' | 'admin' | null> {
        const { data, error } = await supabase
            .from('users')
            .select('role')
            .eq('id', userId)
            .single()

        if (error || !data) return null
        return data.role as 'user' | 'agency' | 'admin'
    }

    static async getAgencyByUserId(userId: string): Promise<{ data: Agency | null, error: any }> {
        const { data, error } = await supabase
            .from('agencies')
            .select('*')
            .eq('user_id', userId)
            .single()

        return { data, error }
    }

    // Social authentication
    static async signInWithGoogle() {
        const { data, error } = await supabase.auth.signInWithOAuth({
            provider: 'google',
            options: {
                redirectTo: `${window.location.origin}/auth/callback`
            }
        })
        return { data, error }
    }

    static async signInWithFacebook() {
        const { data, error } = await supabase.auth.signInWithOAuth({
            provider: 'facebook',
            options: {
                redirectTo: `${window.location.origin}/auth/callback`
            }
        })
        return { data, error }
    }

    // Session management
    static async getSession() {
        const { data: { session }, error } = await supabase.auth.getSession()
        return { session, error }
    }

    static async refreshSession() {
        const { data: { session }, error } = await supabase.auth.refreshSession()
        return { session, error }
    }

    // Real-time auth state changes
    static onAuthStateChange(callback: (event: string, session: any) => void) {
        return supabase.auth.onAuthStateChange(callback)
    }
}
